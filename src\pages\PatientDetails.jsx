import { useState } from "react";
import NoDataPlaceholder from "@components/NoDataPlaceholder";
import Page from "@layout/Page";
import { Box, CircularProgress, Grid, Typography } from "@mui/material";
import { breakpoints, colors, effects, flex, textSizes } from "@styles/vars";
import Avatar from "@ui/Avatar";
import { getNameInitials, formatAddress } from "@utils/helpers";
import React from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import styled from "styled-components";
import { ReactComponent as PatientDocumentIcon } from "@assets/PatientDocument.svg";
import { ReactComponent as FemaleIcon } from "@assets/Female.svg";
import { ReactComponent as MaleIcon } from "@assets/Male.svg";
import { ReactComponent as PrimaryPhysicianIcon } from "@assets/PrimaryPhysician.svg";
import { ReactComponent as PhoneIcon } from "@assets/Phone.svg";
import { ReactComponent as AssignedNurseIcon } from "@assets/AssignedNurse.svg";
import { ReactComponent as AppointmentsHeadingIcon } from "@assets/AppointmentsHeading.svg";
import moment from "moment";
import { bg, borderShadow } from "@components/Widget/style";
import AppointmentListItem from "@components/AppointmentListItem/AppointmentListItem";
import { Tab, TabContainer } from "react-bootstrap";
import TabNav from "@ui/TabNav";
import TabNavItem from "@ui/TabNav/TabNavItem";
import { theme as muiTheme } from "@styles/mui-theme";
import theme from "styled-theming";
import { Nav } from "react-bootstrap";
import CircularProgressWithLabel from "@ui/CircularProgress";
import { CheckCircle } from "@mui/icons-material";

const Label = styled.label`
  font-size: ${textSizes["14"]};
  color: #9ca3af;
  width: fit-content;
  margin-bottom: 4px;
  display: block;
`;

const Card = styled.div`
  box-shadow: ${effects.widgetShadow};
  border-radius: 16px;
  position: relative;
  background-color: ${bg};
  overflow: hidden;
  ${flex.col};
  // min-height: 182px;
  // flex-grow: 1;
  // ${(props) => props.mobile && `height: ${props.mobile}px`};
  // iOS fix
  transform: translate3d(0, 0, 0);

  &.shadow {
    &:before,
    &:after {
      display: block;
    }
  }

  &:before,
  &:after {
    content: "";
    position: absolute;
    top: 0;
    background: ${borderShadow};
    height: 100%;
    width: 24px;
    z-index: 3;
    filter: blur(1px);
    display: none;
  }

  &:before {
    left: -2px;
    transform: ${(props) => (props.dir === "rtl" ? "scaleX(-1)" : "scaleX(1)")};
  }

  &:after {
    right: -2px;
    transform: rotate(180deg) ${(props) => (props.dir === "rtl" ? "scaleX(-1)" : "scaleX(1)")};
  }
`;

const reasonOfRequestBg = theme("theme", {
  light: "#F1F5F9",
  dark: "#1E293B",
});

const ReasonOfRequest = styled(Typography)`
  background-color: ${reasonOfRequestBg};
  padding: 10px 16px;
  border-radius: 4px;
  margin-top: 6px !important;
`;

const GrayedInput = styled(Typography)`
  background-color: ${reasonOfRequestBg};
  padding: 10px 16px;
  border-radius: 4px;
  margin-top: 6px !important;
`;

const pillBgColor = theme("theme", {
  light: "rgba(241, 245, 249, 1)",
  dark: "rgba(74, 83, 92, 0.42)",
});

const PillText = styled(Typography)`
  width: fit-content;
  color: ${colors.primary};
  background-color: ${pillBgColor};
  font-size: 14px !important;
  padding: 10px 12px;
  border-radius: 10px;
  margin-top: 6px !important;
  display: flex;
  gap: 8px;
  align-items: center;
`;

const DocPreview = styled.img`
  height: 40px;
  width: 60px;
  border-radius: 4px;
`;

const TabNavContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  border-radius: 6px;
  overflow: hidden;
  margin-top: 32px;

  .nav-item {
    flex: 1 1 auto; /* Default: desktop, 4 items per row */
  }

  /* Tablet: 2 items per row */
  @media screen and (max-width: 590px) {
    .nav-item {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  /* Mobile: 1 item per row */
  @media screen and (max-width: 360px) {
    .nav-item {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
`;

const StyledTabNav = ({ children }) => {
  return <TabNavContainer as={Nav}>{children}</TabNavContainer>;
};

const PatientDetails = () => {
  const { clientId } = useParams();
  const [activeTab, setActiveTab] = useState("step1");

  const { user: logged_user } = useSelector((state) => state.auth);
  const { clients, caregivers, nurses } = useSelector((state) => state.users);
  const { appointments } = useSelector((state) => state.appointments);

  const client = clients?.find((client) => client.id === clientId);
  const caregiver = caregivers?.find((item) => item?.id === client?.assignedCaregiver);
  const nurse = nurses?.find((item) => item?.id === client?.assignedNurse);
  const client_appointments = appointments?.filter((item) => item?.client === clientId);
  const findCaregiver = (caregiverId) => caregivers?.find((item) => item?.id === caregiverId);

  const findNurse = () => {
    if (logged_user?.role === "NURSE") {
      return logged_user;
    } else if (logged_user?.role === "ADMIN") {
      return nurses?.find((item) => item?.id === client?.assignedNurse);
    }
  };

  function renderGender() {
    switch (client?.gender) {
      case "male":
        return (
          <Typography variant="body1" fontWeight={400} fontSize="16px" display="flex" alignItems="center" gap={1}>
            <MaleIcon style={{ height: 16, width: 16 }} />
            <span>Male</span>
          </Typography>
        );
      case "female":
        return (
          <Typography variant="body1" fontWeight={400} fontSize="16px" display="flex" alignItems="center" gap={1}>
            <FemaleIcon />
            <span>Female</span>
          </Typography>
        );

      default:
        break;
    }
  }

  if (!client) {
    return (
      <Page title="Patient Details">
        <NoDataPlaceholder />
      </Page>
    );
  }

  return (
    <>
      <Page title="Patient Details">
        {/* CLIENT DETAILS CARD */}
        <Card name="PatientDetails">
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "36px" },
              flexDirection: { xs: "column", sm: "row" },
              gap: { xs: 2, lg: 8 },
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: { xs: "center", sm: "flex-start" },
                alignItems: { xs: "center", sm: "flex-start" },
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: ".5rem", sm: "2rem" },
                width: "100%",
              }}
            >
              <Avatar
                alt={client?.name}
                avatar={{ jpg: client?.photo?.url }}
                initals={getNameInitials(client?.firstName, client?.lastName)}
                size={90}
              />
              <div>
                <Typography
                  variant="h6"
                  fontWeight={500}
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "1px",
                    justifyContent: "center",
                    alignItems: {
                      xs: "center",
                      sm: "flex-start",
                      md: "flex-start",
                      lg: "flex-start",
                      xl: "flex-start",
                    },
                  }}
                >
                  {client?.name}
                </Typography>
                <Typography
                  variant="body2"
                  // fontSize="12px"
                >
                  {client?.email}
                </Typography>
                {/* <Typography
                  variant="body1"
                  fontWeight={400}
                  fontSize="12px"
                  sx={{
                    backgroundColor: client?.isActive ? "#00898c1c" : "#FEE2E2",
                    color: client?.isActive ? "#00898c" : "#B91C1C",
                    padding: "4px 12px",
                    borderRadius: "99px",
                    width: "fit-content",
                  }}
                  mt={1}
                >
                  {client?.isActive ? "Active" : "Inactive"}
                </Typography> */}
              </div>
            </Box>

            <Box display="flex" alignItems="center" gap={2}>
              <Typography whiteSpace="nowrap">Profile Completion</Typography>
              <CircularProgressWithLabel value={client?.onboardPercentage || 0} size={50} />
            </Box>
          </Box>
        </Card>

        {/* TABS */}
        <Tab.Container defaultActiveKey="step1" transition={true} activeKey={activeTab} onSelect={setActiveTab}>
          <StyledTabNav>
            <TabNavItem eventKey="step1" title="Profile" handler={() => setActiveTab("step1")} />
            <TabNavItem eventKey="step2" title="Health Assessment" handler={() => setActiveTab("step2")} />
            <TabNavItem eventKey="step3" title="Medical History" handler={() => setActiveTab("step3")} />
            <TabNavItem eventKey="step4" title="Care Plan" handler={() => setActiveTab("step4")} />
            <TabNavItem eventKey="APPOINTMENTS" title="Appointments" handler={() => setActiveTab("APPOINTMENTS")} />
          </StyledTabNav>

          <Tab.Content>
            <Tab.Pane active={activeTab === "step1"} eventKey="step1">
              <ProfileTabContent patient={client} nurse={findNurse()} />
            </Tab.Pane>
            <Tab.Pane active={activeTab === "step2"} eventKey="step2">
              <HealthAssessmentTabContent patient={client} />
            </Tab.Pane>
            <Tab.Pane active={activeTab === "step3"} eventKey="step3">
              <MedicalHistoryTabContent patient={client} />
            </Tab.Pane>
            <Tab.Pane active={activeTab === "step4"} eventKey="step4">
              <CarePlanTabContent patient={client} />
            </Tab.Pane>
            <Tab.Pane active={activeTab === "APPOINTMENTS"} eventKey="APPOINTMENTS">
              <AppointmentsTabContent patient={client} />
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Page>
    </>
  );
};

export default PatientDetails;

const ProfileTabContent = ({ patient, nurse }) => {
  return (
    <>
      <Grid container spacing={2} mt={4}>
        {/* LEFT SIDE */}
        <Grid size={{ xs: 12, sm: 6 }}>
          <Card>
            <Box px={3} py={2}>
              <Grid container rowGap={2}>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Gender
                  </Typography>
                  <Typography textTransform="capitalize" fontWeight={500}>
                    {patient?.gender}
                  </Typography>
                </Grid>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Date of Birth
                  </Typography>
                  <Typography fontWeight={500}>{moment(patient?.dob).format("MMMM DD, YYYY")}</Typography>
                </Grid>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Phone
                  </Typography>
                  <Typography fontWeight={500}>{patient?.phone}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Address
                  </Typography>
                  <Typography fontWeight={500} sx={{ whiteSpace: 'pre-line' }}>
                    {formatAddress(patient?.address)}
                  </Typography>
                </Grid>

                {patient?.allergies?.length ? (
                  <Grid size={12}>
                    <Typography fontSize={14} color="#6B7280">
                      Allergies
                    </Typography>
                    <Typography fontWeight={500}>{patient?.allergies?.join(", ")}</Typography>
                  </Grid>
                ) : null}
                {patient?.knownDiagnoses?.length ? (
                  <Grid size={12}>
                    <Typography fontSize={14} color="#6B7280">
                      Known Diagnoses
                    </Typography>
                    <Typography fontWeight={500}>{patient?.knownDiagnoses?.join(", ")}</Typography>
                  </Grid>
                ) : null}
                {patient?.medications?.length ? (
                  <Grid size={12}>
                    <Typography fontSize={14} color="#6B7280">
                      Medications
                    </Typography>
                    <Typography fontWeight={500}>{patient?.medications?.join(", ")}</Typography>
                  </Grid>
                ) : null}
              </Grid>
            </Box>
          </Card>
        </Grid>
        {/* RIGHT SIDE */}
        <Grid size={{ xs: 12, sm: 6 }} display="flex" flexDirection="column" gap={2}>
          <Card>
            <Box p={3} display="flex" gap={1} flexDirection="column">
              <Box display="flex" gap={2}>
                <Avatar size={50} initals={getNameInitials(nurse?.firstName, nurse?.lastName)} />
                <Box display="flex" justifyContent="center" flexDirection="column">
                  <Typography fontWeight={500}>{nurse?.name}</Typography>
                  <Typography fontSize={12} color="#6B7280">
                    {nurse?.email}
                  </Typography>
                </Box>
              </Box>
              <Box display="flex" gap={1} alignItems="center">
                <AssignedNurseIcon />
                <Typography fontSize={12} color="#6B7280">
                  Assigned Nurse
                </Typography>
              </Box>
            </Box>
          </Card>

          {/* EMERGENCY CONTACT PERSON */}
          <Card>
            <Box p={3} display="flex" flexDirection="column" gap={1}>
              <Box display="flex" gap={2} alignItems="center">
                <PhoneIcon fill={muiTheme.palette.primary.main} />
                <Typography fontSize={18} fontWeight={600}>
                  Emergency Contact Person
                </Typography>
              </Box>
              <Grid container rowGap={2}>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Name
                  </Typography>
                  <Typography textTransform="capitalize" fontWeight={500}>
                    {patient?.emergencyContactPerson?.name || "Not provided"}
                  </Typography>
                </Grid>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Phone
                  </Typography>
                  <Typography textTransform="capitalize" fontWeight={500}>
                    {patient?.emergencyContactPerson?.phone || "Not provided"}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          </Card>

          {/* PRIMARY PHYSICIAN */}
          {patient?.physician?.name || patient?.physician?.email ? (
            <Card>
              <Box p={3} display="flex" flexDirection="column" gap={1}>
                <Box display="flex" gap={2} alignItems="center" mb={0.8}>
                  <PrimaryPhysicianIcon fill={muiTheme.palette.primary.main} />
                  <Typography fontSize={18} fontWeight={600} lineHeight={"18px"}>
                    Primary Physician
                  </Typography>
                </Box>
                <Grid container rowGap={2}>
                  <Grid size={6}>
                    <Typography fontSize={14} color="#6B7280">
                      Name
                    </Typography>
                    <Typography textTransform="capitalize" fontWeight={500} fontSize={12}>
                      {patient?.physician?.name || "(Not provided)"}
                    </Typography>
                  </Grid>
                  <Grid size={6}>
                    <Typography fontSize={14} color="#6B7280">
                      Email
                    </Typography>
                    <Typography textTransform="capitalize" fontWeight={500} fontSize={12}>
                      {patient?.physician?.email || "(Not provided)"}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Card>
          ) : null}

          {/* DOCUMENTS*/}
          <Card>
            <Box p={3} display="flex" flexDirection="column" gap={1}>
              <Box display="flex" gap={2} alignItems="center">
                <PatientDocumentIcon fill={muiTheme.palette.primary.main} />
                <Typography fontSize={18} fontWeight={600}>
                  Documents
                </Typography>
              </Box>
              <Grid container rowGap={2}>
                {patient?.documents?.length
                  ? patient?.documents?.map((item, index) => (
                      <Grid size={6} key={index}>
                        <DocPreview src={item?.url} />
                      </Grid>
                    ))
                  : null}
              </Grid>
            </Box>
          </Card>
        </Grid>
      </Grid>
    </>
  );
};

const HealthAssessmentTabContent = ({ patient }) => {
  if (patient?.onboardPercentage < 50) {
    return <NoDataPlaceholder />;
  }

  return (
    <>
      <Box mt={4}>
        <Card>
          <Box p={3}>
            <Typography color={muiTheme.palette.primary.main} fontSize={18} fontWeight={500}>
              Presenting Complaints & Medical History
            </Typography>

            <Grid container spacing={3} mt={3}>
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <Typography color="#64748B" fontSize={14}>
                  Why is home care being requested?
                </Typography>
                <PillText fontSize={14}>{patient?.reasonToRequest || "Not provided"}</PillText>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <Typography color="#64748B" fontSize={14}>
                  Any recent hospitalizations?
                </Typography>

                <Box display="flex" gap={1} alignItems="baseline">
                  <Typography
                    variant="body1"
                    fontWeight={400}
                    fontSize="12px"
                    sx={{
                      backgroundColor: patient?.isHospitalizedRecently ? "#FEF3C7" : "#fbc3c0",
                      color: patient?.isHospitalizedRecently ? "#B45309" : "#a03636",
                      padding: "4px 12px",
                      borderRadius: "99px",
                      width: "fit-content",
                    }}
                    mt={1}
                  >
                    {patient?.isHospitalizedRecently ? "Yes" : "No"}
                  </Typography>
                  {patient?.isHospitalizedRecently ? (
                    <Typography color="#64748B" fontSize={14}>
                      {patient?.reasonForHospitalization}
                    </Typography>
                  ) : null}
                </Box>
              </Grid>

              <Grid size={12}>
                <Typography color="#64748B" fontSize={14}>
                  Known Diagnoses
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {patient?.knownDiagnoses?.length ? (
                    patient?.knownDiagnoses?.map((item, index) => (
                      <PillText key={index}>
                        <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                        <span>{item}</span>
                      </PillText>
                    ))
                  ) : (
                    <PillText>No diagnoses</PillText>
                  )}
                </Box>
              </Grid>

              <Grid size={12}>
                <Typography color="#64748B" fontSize={14}>
                  Allergies
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {patient?.allergies?.length ? (
                    patient?.allergies?.map((item, index) => <PillText key={index}>{item}</PillText>)
                  ) : (
                    <PillText>No allergies</PillText>
                  )}
                </Box>
              </Grid>

              <Grid size={12}>
                <Typography color="#64748B" fontSize={14}>
                  Medications
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {patient?.medications?.length ? (
                    patient?.medications?.map((item, index) => <PillText key={index}>{item}</PillText>)
                  ) : (
                    <PillText>No medications</PillText>
                  )}
                </Box>
              </Grid>

              <Grid size={12}>
                <Typography color="#64748B" fontSize={14}>
                  Nutritional Concerns or Restrictions
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {patient?.restrictions?.length ? (
                    patient?.restrictions?.map((item, index) => <PillText key={index}>{item}</PillText>)
                  ) : (
                    <PillText>No restrictions</PillText>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Card>
      </Box>
    </>
  );
};

const MedicalHistoryTabContent = ({ patient }) => {
  const textObj = {
    independent: "Independent",
    needAssistance: "Need Assistance",
    bedbound: "Bedbound",
    needHelp: "Needs Help",
    unable: "Unable",
    incontinent: "Incontinent",
    npo: "NPO",
    wheelchair: "Wheelchair",
    walker: "Walker",
    commode: "Commode",
    alert: "Alert",
    confused: "Confused",
    nonVerbal: "Non-verbal",
    stable: "Stable",
    anxious: "Anxious",
    depressed: "Depressed",
    fullTime: "Full-time",
    partTime: "Part-time",
    none: "None",
  };

  if (patient?.onboardPercentage < 75) {
    return <NoDataPlaceholder />;
  }

  return (
    <>
      <Grid container spacing={2} mt={4}>
        {/* LEFT SIDE */}
        <Grid size={{ xs: 12, sm: 6 }}>
          <Card>
            <Box p={3}>
              <Typography color={muiTheme.palette.primary.main} fontSize={18} fontWeight={500} mb={2}>
                Functional & Safety Assessment
              </Typography>
              <Grid container spacing={2} mt={3}>
                <Grid size={{ xs: 12 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Mobility
                  </Typography>
                  <PillText>
                    <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                    {textObj[patient?.mobility]}
                  </PillText>
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Typography fontSize={16} fontWeight={600}>
                    ADLs (Activities of Daily Living)
                  </Typography>
                </Grid>

                <Grid size={12} sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                  <Box>
                    <Typography color="#64748B" fontSize={14}>
                      Bathing
                    </Typography>
                    <PillText>
                      <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                      {textObj[patient?.bathing]}
                    </PillText>
                  </Box>
                  <Box>
                    <Typography color="#64748B" fontSize={14}>
                      Dressing
                    </Typography>
                    <PillText>
                      <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                      {textObj[patient?.dressing]}
                    </PillText>
                  </Box>
                  <Box>
                    <Typography color="#64748B" fontSize={14}>
                      Feeding
                    </Typography>
                    <PillText>
                      <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                      {textObj[patient?.feeding]}
                    </PillText>
                  </Box>
                  <Box>
                    <Typography color="#64748B" fontSize={14}>
                      Toileting
                    </Typography>
                    <PillText>
                      <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                      {textObj[patient?.toileting]}
                    </PillText>
                  </Box>
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Typography fontSize={16} fontWeight={600}>
                    Home Environment
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Any fall risks? (rugs, stairs, poor lighting)
                  </Typography>
                  <PillText width={150}>
                    <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                    {patient?.hasFallRisk ? "Yes" : "No"}
                  </PillText>
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Presence of Pets/Smokers
                  </Typography>
                  <PillText width={150}>
                    <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                    {patient?.presenceOfPetsSmokers ? "Yes" : "No"}
                  </PillText>
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Equipment Needed
                  </Typography>
                  <PillText width={150}>
                    <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                    {textObj[patient?.equipmentNeeded]}
                  </PillText>
                </Grid>
              </Grid>
            </Box>
          </Card>
        </Grid>

        {/* RIGHT SIDE */}
        <Grid size={{ xs: 12, sm: 4 }} display="flex" flexDirection="column" gap={2}>
          <Card>
            <Box p={3} display="flex" gap={1} flexDirection="column">
              <Typography color={muiTheme.palette.primary.main} fontSize={18} fontWeight={500} mb={2}>
                Psychosocial & Cognitive Status
              </Typography>

              <Box
                sx={{
                  display: "grid",
                  gap: 1.5,
                  gridTemplateColumns: {
                    xs: "1fr 1fr", // 2 column on extra-small devices
                    sm: "1fr 1fr", // 2 columns on small devices and up
                    md: "1fr 1fr ", // 2 columns on medium and up (optional)
                  },
                }}
              >
                {[
                  {
                    label: "Orientation",
                    value: textObj[patient?.orientation],
                  },
                  {
                    label: "Mood",
                    value: textObj[patient?.mood],
                  },
                  {
                    label: "Family Support",
                    value: textObj[patient?.familySupport],
                  },
                  {
                    label: "Caregiver present?",
                    value: patient?.isCaregiverPresent ? "Yes" : "No",
                  },
                ].map(({ label, value }) => (
                  <Box key={label} sx={{ width: "100%" }}>
                    <Typography color="#64748B" fontSize={12}>
                      {label}
                    </Typography>
                    <PillText width="100%">
                      <CheckCircle sx={{ height: 16, width: 14, fill: colors.primary }} />
                      {value}
                    </PillText>
                  </Box>
                ))}
              </Box>
            </Box>
          </Card>
        </Grid>
      </Grid>
    </>
  );
};

const CarePlanTabContent = ({ patient }) => {
  if (patient?.onboardPercentage < 100) {
    return <NoDataPlaceholder />;
  }

  return (
    <>
      <Grid container spacing={2} mt={4}>
        {/* LEFT SIDE */}
        <Grid size={{ xs: 12, sm: 6 }}>
          <Card>
            <Box p={3}>
              <Typography color={muiTheme.palette.primary.main} fontSize={18} fontWeight={500} mb={2}>
                Nursing Assessment
              </Typography>
              <Grid container spacing={2} mt={3}>
                <Grid size={{ xs: 12 }}>
                  <Typography fontSize={16} fontWeight={600}>
                    Vital Signs
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Blood Pressure (mmHg)
                  </Typography>
                  <PillText width="100%">{patient?.bloodPressure}</PillText>
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Temperature (F)
                  </Typography>
                  <PillText width="100%">{patient?.temperature}</PillText>
                </Grid>

                {/* <Grid size={{ xs: 12, sm: 4 }}></Grid> */}

                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Pulse (bpm)
                  </Typography>
                  <PillText width="100%">{patient?.pulse}</PillText>
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography color="#64748B" fontSize={14}>
                    SpO2 (%)
                  </Typography>
                  <PillText width="100%">{patient?.spo2}</PillText>
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Blood Sugar (mg/dL)
                  </Typography>
                  <PillText width="100%">{patient?.bloodSugar}</PillText>
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Labs Needed
                  </Typography>
                  <PillText width="100%">
                    <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                    {patient?.needLabs ? "Yes" : "No"}
                  </PillText>
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Med. admin required?
                  </Typography>
                  <PillText width="100%">
                    <CheckCircle sx={{ height: 16, width: 16, fill: colors.primary }} />
                    {patient?.medAdminRequired ? "Yes" : "No"}
                  </PillText>
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Typography color="#64748B" fontSize={14}>
                    Wound or skin issues
                  </Typography>
                  <PillText width="100%">{patient?.wounds}</PillText>
                </Grid>
              </Grid>
            </Box>
          </Card>
        </Grid>

        {/* RIGHT SIDE */}
        <Grid size={{ xs: 12, sm: 6 }} display="flex" flexDirection="column" gap={2}>
          <Card>
            <Box p={3} display="flex" gap={1} flexDirection="column">
              <Typography color={muiTheme.palette.primary.main} fontSize={18} fontWeight={500} mb={2}>
                Recommended Services
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                {patient?.recommendedServices?.length
                  ? patient?.recommendedServices?.map((item, key) => <PillText key={key}>{item}</PillText>)
                  : null}
              </Box>
            </Box>
          </Card>

          {/* CARE PLAN DOCUMENT */}
          {patient?.carePlan?.url && (
            <Card>
              <Box p={3} display="flex" gap={1} flexDirection="column">
                <Typography color={muiTheme.palette.primary.main} fontSize={18} fontWeight={500} mb={2}>
                  Care Plan Document
                </Typography>
                <Box
                  display="flex"
                  alignItems="center"
                  gap={2}
                  sx={{
                    cursor: 'pointer',
                    padding: '12px',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    '&:hover': {
                      backgroundColor: '#f5f5f5'
                    }
                  }}
                  onClick={() => window.open(patient?.carePlan?.url, '_blank')}
                >
                  <PatientDocumentIcon fill={muiTheme.palette.primary.main} />
                  <Box>
                    <Typography fontWeight={500} fontSize={14}>
                      Care Plan
                    </Typography>
                    <Typography fontSize={12} color="#6B7280">
                      Click to open
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Card>
          )}
        </Grid>
      </Grid>
    </>
  );
};

const AppointmentsTabContent = () => {
  const { clientId } = useParams();
  const { caregivers } = useSelector((state) => state.users);
  const { appointments } = useSelector((state) => state.appointments);

  const client_appointments = appointments?.filter((item) => item?.client === clientId);

  // Sort appointments by startTimeStamp (earliest first)
  const sortedClientAppointments = client_appointments?.sort((a, b) => {
    const timestampA = a?.startTimeStamp || 0;
    const timestampB = b?.startTimeStamp || 0;
    
    // Sort in ascending order (earliest dates first)
    return timestampA - timestampB;
  });

  const findCaregiver = (caregiverId) => caregivers?.find((item) => item?.id === caregiverId);

  return (
    <>
      <Box my={4}>
        <Card name="PatientAppointments">
          {sortedClientAppointments?.length ? (
            <Box p={2} display={"flex"} flexDirection="column" gap={2}>
              {sortedClientAppointments?.map((item) => (
                <AppointmentListItem key={item?.id} appointment={item} caregiver={findCaregiver(item?.caregiver)} />
              ))}
            </Box>
          ) : (
            <NoDataPlaceholder />
          )}
        </Card>
      </Box>
    </>
  );
};
