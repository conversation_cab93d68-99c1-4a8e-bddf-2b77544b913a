import usePageIsOverflow from "@hooks/usePageIsOverflow";
import useWindowSize from "@hooks/useWindowSize";
import Panel from "@layout/Panel";
import Sidebar from "@layout/Sidebar";
import ScrollProgress from "@ui/ScrollProgress";
import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { Navigate, Outlet } from "react-router-dom";
import { ThemeProvider as MuiThemeProvider } from "@mui/material/styles";
import { theme } from "@styles/mui-theme";
import { Suspense } from "react";
import WidgetsLoader from "@components/WidgetsLoader";
import { auth } from "../../config/firebase.config";

const ProtectedRoutes = () => {
  const currentUser = auth.currentUser;
  const appRef = useRef(null);
  const isOverflow = usePageIsOverflow();
  const { user, isLoading } = useSelector((state) => state.auth);

  

  useEffect(() => {
    if (appRef.current) {
      appRef.current.scrollTo(0, 0);
    }
  }, []);

   // If we have userId and still loading, show loading state
  if (isLoading) {
    return <WidgetsLoader />;
  }

  if(!currentUser?.uid){
    return <Navigate to="/login" />;
  }

  // If we have userId and user data, show protected content

    return (
      <>
        <MuiThemeProvider theme={theme}>
          <div className="app" ref={appRef}>
            {isOverflow ? <ScrollProgress /> : null}
            <Sidebar />
            <div className="app_content">
              <Panel />
              <Suspense fallback={<WidgetsLoader />}>
                <Outlet />
              </Suspense>
            </div>
          </div>
        </MuiThemeProvider>
      </>
    );
  
};

export default ProtectedRoutes;
